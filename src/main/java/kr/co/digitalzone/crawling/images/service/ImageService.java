package kr.co.digitalzone.crawling.images.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import kr.co.digitalzone.dto.CrawlingCountDto;
import kr.co.digitalzone.dto.ImageCollectionReqDto;
import kr.co.digitalzone.dto.ImageCollectionResultDto;
import kr.co.digitalzone.dto.KeywordCountDto;
import kr.co.digitalzone.entity.TargetData;
import kr.co.digitalzone.repository.ImageMetadataRepository;
import kr.co.digitalzone.repository.TargetDataRepository;
import kr.co.digitalzone.user.common.dto.ResponseDto;
import lombok.Builder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
@Service
@Slf4j
@RequiredArgsConstructor
public class ImageService {
//    private static final Logger log = LoggerFactory.getLogger(ImageService.class);
//    private int collectedImagesCount = 0;

    // Repository 주입
    private final ImageMetadataRepository imageMetadataRepository;
    private final TargetDataRepository targetDataRepository;

    // 스레드 풀 (동시 처리를 위한)
    private final ExecutorService executorService = Executors.newFixedThreadPool(5);

//    public void updateCollectedImagesCount(int count) {
//        this.collectedImagesCount += count;
//    }
//
//    public int getCollectedImagesCount() {
//        return this.collectedImagesCount;
//    }


//    private int totalImages = 0;
//    private int collectedImages = 0;
//    private String currentKeyword = "";
//    private String keyword;


//    public String collectImages(String keyword, int collect_count) {
//        this.totalImages = collect_count;
//        this.collectedImages = 0;
//        this.currentKeyword = keyword;
//
//
////        로컬
//        String pythonScriptPath = "/Users/<USER>/rd/labelingService/labelingBackend/total_crawler.py"; // Python 파일 경로
//        String imageOutputDir = "/Users/<USER>/rd/labeling/crawling";
//        // 배포
////        String pythonScriptPath = "/volume1/pj1/backEnd/labeler/total_crawler.py";  // Docker 컨테이너 내부의 Python 파일 경로
////        String imageOutputDir = "/volume1/pj1/datalake/image"; // 이미지가 저장될 디렉토리 경로
//
//
//        List<String> command = new ArrayList<>();
//        command.add("bash");
//        command.add("-c");
//        // local
//         command.add("source /Users/<USER>/rd/labelingService/labelingBackend/labeling/bin/activate && " +  // 가상 환경 활성화
//        // 배포
////        command.add("source /volume1/pj1/backEnd/labeling/bin/activate && " +  // 가상 환경 활성화
//                "python " + pythonScriptPath + " " + keyword + " " + collect_count);  // 파이썬 스크립트 실행
//
//
//        try {
//            ProcessBuilder processBuilder = new ProcessBuilder(command);
////            processBuilder.redirectErrorStream(true); // 오류 출력을 표준 출력으로 리디렉션
//            Process process = processBuilder.start();
//
//            // 프로세스의 출력을 읽어옴
//            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
//            StringBuilder output = new StringBuilder();//
//            String line;
//            while ((line = reader.readLine()) != null) {
////                log.info(line); // 로그 출력
//                System.out.println(line);
//                System.out.flush(); // 버퍼를 비워 로그를 즉시 출력
//
//
//                if (line.contains("완료")) { // 이미지 수집 로그를 감지
//                    collectedImages++; // 수집된 이미지 수 증가
//                }
//            }
//
//                // 이미지 수집 작업 수행
//                // ...
//
////                // 이미지 수집 완료 시점에 collectedImages 증가
////                collectedImages++; // 수집된 이미지 수 증가
////                System.out.println(collectedImages + " 번째 수집완료");
//
//            // 프로세스가 종료될 때까지 대기
////            int exitCode = process.waitFor();
////            if (exitCode != 0) {
////                throw new RuntimeException("Python script execution failed with exit code: " + exitCode);
////            }
//            int exitCode = process.waitFor();
//            switch (exitCode) {
//                case 0:
//                    // 정상 종료
//                    break;
//                case 1:
//                    throw new IllegalArgumentException("Python script execution failed with exit code 1: Invalid arguments");
//                case 2:
//                    throw new IOException("Python script execution failed with exit code 2: I/O error");
//                case 3:
//                    throw new SecurityException("Python script execution failed with exit code 3: Security error");
//                default:
//                    throw new RuntimeException("Python script execution failed with exit code: " + exitCode);
//            }
//
//            // 이미지 파일 목록을 읽어옴
//            List<String> imageFiles = Files.list(Paths.get(imageOutputDir))
//                    .map(path -> path.getFileName().toString())
//                    .collect(Collectors.toList());
//
//
//            return "이미지 수집이 완료되었습니다. 수집된 이미지 파일: " + String.join(", ", imageFiles);
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw new RuntimeException("Error executing Python script: " + e.getMessage());
//        }
//    }

//    public Map<String, Object> getProgress() {
//        // 수집 상태 정보 반환
//        Map<String, Object> progress = new HashMap<>();
//        progress.put("keyword", currentKeyword);
//        progress.put("total", totalImages);
//        progress.put("collected", collectedImages);
//        progress.put("progress", (double) collectedImages / totalImages * 100);
//        return progress;
//
//    }
////    //배포
//////    private static final String IMAGE_OUTPUT_DIR = "/volume1/pj1/datalake/image";
//    // 로컬
//    private static final String IMAGE_OUTPUT_DIR = "/Users/<USER>/rd/labeling/crawling";
//
//    // 이미지 수집 상세 현황 조회
//    public Map<String, Object> getDetail() {
//        Map<String, Object> detail = new HashMap<>();
//        File outputDir = new File(IMAGE_OUTPUT_DIR);
//        // 이미지 수집 디렉터리 목록 조회
//        if (outputDir.exists() && outputDir.isDirectory()) {
//            File[] keywordDirs = outputDir.listFiles(File::isDirectory);
//            if (keywordDirs != null) {
//                for (File keywordDir : keywordDirs) {
//                    File[] imageFiles = keywordDir.listFiles((dir, name) -> name.toLowerCase().endsWith(".jpg"));
//                    if (imageFiles != null) {
//                        detail.put(keywordDir.getName(), imageFiles.length);
//                    }
//                }
//            }
//        }
//        return  detail;
//    }

    /**
     * 개별 이미지 수집 처리
     * @param request 수집 요청
     * @return 수집 결과
     */
    public ImageCollectionResultDto processImageCollection(ImageCollectionReqDto request) {

        log.info("이미지 수집 시작 - 소스: {}, 키워드: {}, 목표: {}",
                request.getSource(), request.getKeyword(), request.getTarget_count());

        try {
            // Python 스크립트 실행을 위한 명령어 구성
            String pythonScriptPath = getPythonScriptPath();
            String imageOutputDir = getImageOutputDir();

            List<String> command = buildPythonCommand(pythonScriptPath, request);

            // Python 스크립트 실행
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            Process process = processBuilder.start();

            // 프로세스 출력 모니터링
            int collectedCount = monitorProcessOutput(process, request);

            // 프로세스 완료 대기
            int exitCode = process.waitFor();

            if (exitCode == 0) {
                return ImageCollectionResultDto.builder()
                        .source(request.getSource())
                        .keyword(request.getKeyword())
                        .status("수집 중")
                        .build();
            } else {
                log.error("Python 스크립트 실행 실패 - 종료 코드: {}", exitCode);
                return ImageCollectionResultDto.builder()
                        .source(request.getSource())
                        .keyword(request.getKeyword())
                        .status("실패")
                        .build();
            }

        } catch (Exception e) {
            log.error("이미지 수집 중 오류 발생", e);
            return ImageCollectionResultDto.builder()
                    .source(request.getSource())
                    .keyword(request.getKeyword())
                    .status("오류")
                    .build();
        }
    }

    /**
     * Python 명령어 구성 (단일 요청용) - 임시 파일 사용
     */
    private List<String> buildPythonCommand(String pythonScriptPath, ImageCollectionReqDto request) {
        // 단일 요청을 리스트로 변환하여 다중 요청 메서드 재사용
        List<ImageCollectionReqDto> requests = List.of(request);
        return buildPythonCommand(pythonScriptPath, requests);
    }

    /**
     * Python 명령어 구성 (다중 요청용) - 임시 파일 사용
     */
    private List<String> buildPythonCommand(String pythonScriptPath, List<ImageCollectionReqDto> requests) {
        try {
            // JSON 데이터를 문자열로 변환
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonString = objectMapper.writeValueAsString(requests);
            log.info("생성된 JSON 문자열 (다중): {}", jsonString);

            // 임시 파일에 JSON 데이터 저장
            File tempFile = File.createTempFile("image_collection_", ".json");
            tempFile.deleteOnExit(); // JVM 종료 시 자동 삭제

            try (java.io.FileWriter writer = new java.io.FileWriter(tempFile)) {
                writer.write(jsonString);
            }

            log.info("임시 JSON 파일 생성: {}", tempFile.getAbsolutePath());

            // Python 명령어 구성
            List<String> command = new ArrayList<>();
            command.add("bash");
            command.add("-c");

            String pythonCommand = String.format(
                    "source %s && python %s \"$(cat %s)\"",
                    getVirtualEnvPath(),
                    pythonScriptPath,
                    tempFile.getAbsolutePath()
            );

            command.add(pythonCommand);
            log.info("최종 Python 명령어 (다중): {}", pythonCommand);
            return command;

        } catch (Exception e) {
            throw new RuntimeException("Python 명령어 구성 실패", e);
        }
    }

    /**
     * 프로세스 출력 모니터링 및 수집 개수 카운팅
     */
    private int monitorProcessOutput(Process process, ImageCollectionReqDto request) {
        int collectedCount = 0;

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                log.info("Python 출력: {}", line);

                // 수집 완료 로그 감지 (Python 스크립트의 출력 형식에 따라 조정 필요)
                if (line.contains("이미지 다운로드 완료") || line.contains("완료")) {
                    collectedCount++;
                }

                // 진행률 로그 등 추가 처리 가능
                if (line.contains("진행률")) {
                    // 진행률 정보 처리
                }
            }
        } catch (Exception e) {
            log.error("프로세스 출력 모니터링 중 오류", e);
        }

        return Math.min(collectedCount, request.getTarget_count());
    }




    /**
     * 환경별 경로 설정 메서드들
     */
    // Python 스크립트 경로
    private String getPythonScriptPath() {
        // 로컬 환경
        return "/Users/<USER>/rd/labelingService/labelingBackend/total_crawler.py";
        // 배포 환경
//         return "/volume1/pj1/backEnd/labeler/total_crawler.py";
    }

    // 가상환경 경로
    private String getVirtualEnvPath() {
        // 로컬 환경
        return "/Users/<USER>/rd/labelingService/labelingBackend/labeling/bin/activate";
        // 배포 환경
//         return "/volume1/pj1/backEnd/labeling/bin/activate";
    }

    // 이미지 저장 경로
    private String getImageOutputDir() {
        // 로컬 환경
        return "/Users/<USER>/rd/labeling/crawling";
        // 배포 환경
//         return "/volume1/pj1/datalake/image";
    }

    /**
     * 다중 이미지 수집 처리 (비동기 방식 - 프로세스 실행 상태 확인 후 응답)
     */
    public List<ImageCollectionResultDto> collectImages(List<ImageCollectionReqDto> requests) {
        try {
            // Python 스크립트 실행을 위한 명령어 구성
            String pythonScriptPath = getPythonScriptPath();
            List<String> command = buildPythonCommand(pythonScriptPath, requests);

            // 실행할 명령어 로그 출력 (디버깅용)
            log.info("실행할 Python 명령어: {}", String.join(" ", command));
            log.info("Python 스크립트 경로: {}", pythonScriptPath);
            log.info("가상환경 경로: {}", getVirtualEnvPath());

            // 파일 존재 여부 확인
            if (!checkRequiredFiles()) {
                log.error("필수 파일들이 존재하지 않음");
                return requests.stream()
                        .map(req -> ImageCollectionResultDto.builder()
                                .source(req.getSource())
                                .keyword(req.getKeyword())
                                .status("실패")
                                .build())
                        .collect(Collectors.toList());
            }

            // Python 실행 환경 간단 검증
            if (!isPythonScriptExecutable(command)) {
                log.error("Python 실행 환경 문제 감지");
                return requests.stream()
                        .map(req -> ImageCollectionResultDto.builder()
                                .source(req.getSource())
                                .keyword(req.getKeyword())
                                .status("실패")
                                .build())
                        .collect(Collectors.toList());
            }

            // Python 스크립트를 비동기로 실행
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            processBuilder.redirectErrorStream(true); // 에러 스트림을 표준 출력으로 리다이렉트
            Process process = processBuilder.start();

            log.info("Python 스크립트 실행 시작 - 요청 개수: {}", requests.size());

            // 프로세스가 실제로 시작되었는지 확인 (짧은 시간 대기)
            boolean processStartedSuccessfully = verifyProcessStartup(process);

            if (!processStartedSuccessfully) {
                log.error("Python 프로세스 시작 실패 또는 즉시 종료됨");
                // 프로세스가 아직 살아있다면 종료
                if (process.isAlive()) {
                    process.destroyForcibly();
                }
                return requests.stream()
                        .map(req -> ImageCollectionResultDto.builder()
                                .source(req.getSource())
                                .keyword(req.getKeyword())
                                .status("실패")
                                .build())
                        .collect(Collectors.toList());
            }

            // 백그라운드에서 프로세스 모니터링 (로그만)
            CompletableFuture.runAsync(() -> {
                try {
                    int exitCode = process.waitFor();
                    if (exitCode == 0) {
                        log.info("이미지 수집 프로세스 정상 완료");
                    } else {
                        log.error("Python 스크립트 실행 실패 - 종료 코드: {}", exitCode);
                    }
                } catch (InterruptedException e) {
                    log.error("프로세스 대기 중 인터럽트 발생", e);
                    Thread.currentThread().interrupt();
                }
            }, executorService);

            // 프로세스가 정상적으로 시작되었으므로 "수집 중" 상태로 응답 반환
            log.info("Python 프로세스 정상 시작 확인 완료");
            return requests.stream()
                    .map(req -> ImageCollectionResultDto.builder()
                            .source(req.getSource())
                            .keyword(req.getKeyword())
                            .status("수집 중")
                            .build())
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("이미지 수집 시작 중 오류 발생", e);
            return requests.stream()
                    .map(req -> ImageCollectionResultDto.builder()
                            .source(req.getSource())
                            .keyword(req.getKeyword())
                            .status("실패")
                            .build())
                    .collect(Collectors.toList());
        }
    }
    /**
     * Python 프로세스 시작 상태 검증
     * 프로세스가 실제로 시작되어 정상 실행 중인지 확인
     */
    private boolean verifyProcessStartup(Process process) {
        try {
            // 프로세스가 즉시 종료되지 않았는지 확인 (3초 대기)
            Thread.sleep(3000);

            if (!process.isAlive()) {
                log.error("Python 프로세스가 시작 후 즉시 종료됨");

                // 프로세스 종료 코드 확인
                try {
                    int exitCode = process.exitValue();
                    log.error("프로세스 종료 코드: {}", exitCode);
                } catch (IllegalThreadStateException e) {
                    log.error("프로세스 종료 코드를 가져올 수 없음");
                }

                // 프로세스 출력 및 에러 스트림 읽기
                captureProcessOutput(process);
                return false;
            }

            // 프로세스 출력을 확인하여 정상 시작 여부 판단
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                StringBuilder output = new StringBuilder();
                String line;

                // 사용 가능한 모든 출력 읽기
                while (reader.ready() && (line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                    log.info("Python 프로세스 출력: {}", line);

                    // 에러 메시지가 포함되어 있는지 확인
                    if (line.toLowerCase().contains("error") ||
                        line.toLowerCase().contains("exception") ||
                        line.toLowerCase().contains("failed") ||
                        line.toLowerCase().contains("traceback") ||
                        line.toLowerCase().contains("no such file")) {
                        log.error("Python 프로세스 시작 시 에러 감지: {}", line);
                        return false;
                    }
                }

                if (output.length() > 0) {
                    log.info("Python 프로세스 전체 출력:\n{}", output.toString());
                }
            }

            log.info("Python 프로세스 정상 시작 확인됨");
            return true;

        } catch (InterruptedException e) {
            log.error("프로세스 시작 검증 중 인터럽트 발생", e);
            Thread.currentThread().interrupt();
            return false;
        } catch (Exception e) {
            log.error("프로세스 시작 검증 중 오류 발생", e);
            return false;
        }
    }

    /**
     * 프로세스 출력 캡처 (디버깅용)
     */
    private void captureProcessOutput(Process process) {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            log.error("=== 프로세스 출력 캡처 시작 ===");
            while ((line = reader.readLine()) != null) {
                log.error("프로세스 출력: {}", line);
            }
            log.error("=== 프로세스 출력 캡처 종료 ===");
        } catch (Exception e) {
            log.error("프로세스 출력 캡처 중 오류", e);
        }
    }

    /**
     * 필수 파일들의 존재 여부 확인
     */
    private boolean checkRequiredFiles() {
        String pythonScriptPath = getPythonScriptPath();
        String virtualEnvPath = getVirtualEnvPath();

        // Python 스크립트 파일 존재 확인
        File pythonScript = new File(pythonScriptPath);
        if (!pythonScript.exists()) {
            log.error("Python 스크립트 파일이 존재하지 않음: {}", pythonScriptPath);
            return false;
        }

        // 가상환경 활성화 스크립트 존재 확인
        File virtualEnvScript = new File(virtualEnvPath);
        if (!virtualEnvScript.exists()) {
            log.error("가상환경 활성화 스크립트가 존재하지 않음: {}", virtualEnvPath);
            return false;
        }

        log.info("필수 파일들 존재 확인 완료");
        return true;
    }

    /**
     * Python 스크립트 실행 상태 확인 (간단한 검증)
     */
    private boolean isPythonScriptExecutable(List<String> command) {
        try {
            ProcessBuilder testBuilder = new ProcessBuilder(command.get(0), "-c", "echo 'test'");
            Process testProcess = testBuilder.start();
            int exitCode = testProcess.waitFor();
            return exitCode == 0;
        } catch (Exception e) {
            log.warn("Python 실행 환경 확인 실패: {}", e.getMessage());
            return false;
        }
    }






    // 데이터 수집 현황 조회 (하드코딩)
    public Map<String, Object> getCrawlingStatus() {
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "실시간 수집 현황 조회 성공");

        List<Map<String, Object>> dataList = new ArrayList<>();

        // 첫 번째 데이터
        Map<String, Object> data1 = new HashMap<>();
        data1.put("keyword", "고양이");
        data1.put("start_date", "2025-06-04 15:01");
        data1.put("end_date", "2025-06-04 19:02");
        data1.put("source", "google");
        data1.put("collected_count", 58);
        data1.put("target_count", 100);
        data1.put("total_count", 201);
        data1.put("status", "진행중");
        data1.put("job_id", "123");
        dataList.add(data1);

        // 두 번째 데이터
        Map<String, Object> data2 = new HashMap<>();
        data2.put("keyword", "강아지");
        data2.put("start_date", "2025-06-01 15:01");
        data2.put("end_date", "2025-06-01 19:02");
        data2.put("source", "unsplash");
        data2.put("collected_count", 28);
        data2.put("target_count", 50);
        data2.put("total_count", 201);
        data2.put("status", "진행중");
        data1.put("job_id", "111");
        dataList.add(data2);

        response.put("data", dataList);
        return response;
    }

    /**
     * 특정 job_id에 대한 데이터 수집 현황 조회
     * @param jobId 조회할 작업 ID
     * @return 수집 현황 데이터
     */
    public Map<String, Object> getCrawlingCount(String jobId) {
        log.info("데이터 수집현황 조회 시작 - jobId: {}", jobId);

        try {
            // target_data 테이블에서 특정 job_id의 데이터만 조회
            List<TargetData> targetDataList = targetDataRepository.findByJobId(jobId);

            List<CrawlingCountDto> resultList = new ArrayList<>();

            for (TargetData targetData : targetDataList) {
                // 해당 job_id로 수집된 이미지 개수만 조회
                Long collectedCount = imageMetadataRepository.countByJobId(targetData.getJobId());

                CrawlingCountDto dto = CrawlingCountDto.builder()
                        .jobId(targetData.getJobId())
                        .source(targetData.getSource())
                        .keyword(targetData.getKeyword())
                        .targetCount(targetData.getTargetCount())
                        .collectedCount(collectedCount)
                        .build();

                resultList.add(dto);
            }

            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "데이터 수집현황 조회 성공");
            response.put("data", resultList);

            log.info("데이터 수집현황 조회 완료 - jobId: {}, 총 항목 수: {}", jobId, resultList.size());

            return response;

        } catch (Exception e) {
            log.error("데이터 수집현황 조회 중 오류 발생", e);

            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 500);
            errorResponse.put("message", "데이터 수집현황 조회 실패: " + e.getMessage());
            errorResponse.put("data", new ArrayList<>());

            return errorResponse;
        }
    }

    // 키워드별 데이터 보유 현황 조회 (실제 DB 조회)
    public Map<String, Object> getKeywordCount() {
        log.info("키워드별 데이터 보유 현황 조회 시작");

        try {
            List<KeywordCountDto> keywordCounts = imageMetadataRepository.findKeywordCounts();

            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "전체 키워드 보유 현황 조회 성공");
            response.put("data", keywordCounts);

            log.info("키워드별 데이터 보유 현황 조회 완료 - 총 키워드 수: {}", keywordCounts.size());

            return response;

        } catch (Exception e) {
            log.error("키워드별 데이터 보유 현황 조회 중 오류 발생", e);

            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 500);
            errorResponse.put("message", "전체 키워드 보유 현황 조회 실패: " + e.getMessage());
            errorResponse.put("data", new ArrayList<>());

            return errorResponse;
        }
    }

    /**
     * 특정 jobId에 해당하는 데이터 삭제
     * @param jobId 삭제할 작업 ID
     * @return 삭제 결과
     */
    @Transactional
    public Map<String, Object> deleteCrawlingData(String jobId) {
        log.info("데이터 삭제 시작 - jobId: {}", jobId);

        Map<String, Object> response = new HashMap<>();

        try {
            // 1. 해당 jobId가 존재하는지 확인
            List<TargetData> targetDataList = targetDataRepository.findByJobId(jobId);
            if (targetDataList.isEmpty()) {
                log.warn("삭제할 데이터가 존재하지 않음 - jobId: {}", jobId);
                response.put("code", 404);
                response.put("message", "해당 jobId의 데이터가 존재하지 않습니다.");
                return response;
            }

            // 2. 삭제 전 데이터 개수 확인 (로그용)
            Long imageCount = imageMetadataRepository.countByJobId(jobId);
            log.info("삭제 예정 데이터 - jobId: {}, 타겟 데이터: {}개, 이미지 메타데이터: {}개",
                    jobId, targetDataList.size(), imageCount);

            // 3. 이미지 메타데이터 삭제
            imageMetadataRepository.deleteByJobId(jobId);
            log.info("이미지 메타데이터 삭제 완료 - jobId: {}", jobId);

            // 4. 타겟 데이터 삭제
            targetDataRepository.deleteByJobId(jobId);
            log.info("타겟 데이터 삭제 완료 - jobId: {}", jobId);

            response.put("code", 200);
            response.put("message", "데이터 삭제 성공");
            response.put("deletedJobId", jobId);
            response.put("deletedImageCount", imageCount);
            response.put("deletedTargetDataCount", targetDataList.size());

            log.info("데이터 삭제 완료 - jobId: {}", jobId);

        } catch (Exception e) {
            log.error("데이터 삭제 중 오류 발생 - jobId: {}", jobId, e);

            response.put("code", 500);
            response.put("message", "데이터 삭제 실패: " + e.getMessage());
            response.put("jobId", jobId);
        }

        return response;
    }

}



